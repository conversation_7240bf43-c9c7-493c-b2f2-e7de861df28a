using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SubscriptionManagement.Application.Commands.CreateTaxCategory;
using SubscriptionManagement.Application.Commands.CreateTaxExemption;
using SubscriptionManagement.Application.Commands.SetGlobalTaxConfiguration;
using SubscriptionManagement.Application.Commands.SetPlanTaxConfiguration;
using SubscriptionManagement.Application.DTOs;
using SubscriptionManagement.Application.Queries.GetGlobalTaxConfigurations;
using SubscriptionManagement.Application.Queries.GetTaxCategories;
using SubscriptionManagement.Domain.Enums;
using System.Security.Claims;

namespace SubscriptionManagement.API.Controllers.Admin
{
   // [Authorize(Roles = "Admin")]
    [Route("api/admin/tax")]
    [ApiController]
    [AllowAnonymous]
    public class AdminTaxController : BaseController
    {
        /// <summary>
        /// Set global tax configuration
        /// </summary>
        [HttpPost("global-configurations")]
        [ProducesResponseType(typeof(Guid), StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> SetGlobalTaxConfiguration([FromBody] SetGlobalTaxConfigurationDto request)
        {
            return await SetGlobalTaxConfigurationInternal(request, true);
        }

        /// <summary>
        /// Set global tax configuration (Debug version without auth)
        /// </summary>
        [HttpPost("global-configurations/debug")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(Guid), StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> SetGlobalTaxConfigurationDebug([FromBody] SetGlobalTaxConfigurationDto request)
        {
            return await SetGlobalTaxConfigurationInternal(request, false);
        }

        private async Task<IActionResult> SetGlobalTaxConfigurationInternal([FromBody] SetGlobalTaxConfigurationDto request, bool requireAuth)
        {
            try
            {
                // Validate the request
                if (request == null)
                {
                    return BadRequest(new { message = "Request body cannot be null" });
                }

                // Additional validation
                if (request.ApplicableRegions == null || !request.ApplicableRegions.Any())
                {
                    return BadRequest(new { message = "At least one applicable region must be specified" });
                }

                if (request.Rate < 0 || request.Rate > 100)
                {
                    return BadRequest(new { message = "Tax rate must be between 0 and 100 percent" });
                }

                if (request.Priority < 0)
                {
                    return BadRequest(new { message = "Priority cannot be negative" });
                }

                // Get current user ID with proper error handling
                Guid currentUserId;
                if (requireAuth)
                {
                    try
                    {
                        currentUserId = GetCurrentUserId();
                    }
                    catch (UnauthorizedAccessException)
                    {
                        return Unauthorized(new { message = "User authentication required" });
                    }
                }
                else
                {
                    // For debug endpoint, use a default user ID
                    currentUserId = Guid.Parse("00000000-0000-0000-0000-000000000001");
                }

                var command = new SetGlobalTaxConfigurationCommand
                {
                    TaxType = request.TaxType,
                    Rate = request.Rate,
                    IsIncluded = request.IsIncluded,
                    ApplicableRegions = request.ApplicableRegions,
                    EffectiveDate = request.EffectiveDate,
                    ExpirationDate = request.ExpirationDate,
                    Priority = request.Priority,
                    Description = request.Description,
                    CreatedByUserId = currentUserId
                };

                var configurationId = await Mediator.Send(command);
                return CreatedAtAction(nameof(GetGlobalTaxConfiguration), new { id = configurationId }, configurationId);
            }
            catch (FluentValidation.ValidationException validationEx)
            {
                var errors = validationEx.Errors.Select(e => new
                {
                    Property = e.PropertyName,
                    Error = e.ErrorMessage
                }).ToList();
                return BadRequest(new { message = "Validation failed", errors });
            }
            catch (SubscriptionManagement.Domain.Exceptions.SubscriptionDomainException domainEx)
            {
                return BadRequest(new { message = domainEx.Message });
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get global tax configurations
        /// </summary>
        [HttpGet("global-configurations")]
        [ProducesResponseType(typeof(List<GlobalTaxConfigurationDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetGlobalTaxConfigurations(
            [FromQuery] bool activeOnly = true,
            [FromQuery] string? region = null,
            [FromQuery] TaxType? taxType = null,
            [FromQuery] DateTime? effectiveDate = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            try
            {
                var query = new GetGlobalTaxConfigurationsQuery
                {
                    ActiveOnly = activeOnly,
                    Region = region,
                    TaxType = taxType,
                    EffectiveDate = effectiveDate,
                    Page = page,
                    PageSize = pageSize
                };

                var configurations = await Mediator.Send(query);
                return Ok(configurations);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get specific global tax configuration
        /// </summary>
        [HttpGet("global-configurations/{id}")]
        [ProducesResponseType(typeof(GlobalTaxConfigurationDto), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetGlobalTaxConfiguration(Guid id)
        {
            try
            {
                // This would need a specific query implementation
                return NotFound("Global tax configuration not found");
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Set plan-specific tax configuration
        /// </summary>
        [HttpPost("plans/{planId}/configurations")]
        [ProducesResponseType(typeof(Guid), StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> SetPlanTaxConfiguration(Guid planId, [FromBody] SetPlanTaxConfigurationDto request)
        {
            try
            {
                var command = new SetPlanTaxConfigurationCommand
                {
                    PlanId = planId,
                    TaxType = request.TaxType,
                    Rate = request.Rate,
                    IsIncluded = request.IsIncluded,
                    ApplicableRegions = request.ApplicableRegions,
                    EffectiveDate = request.EffectiveDate,
                    ExpirationDate = request.ExpirationDate,
                    Notes = request.Notes,
                    CreatedByUserId = GetCurrentUserId()
                };

                var configurationId = await Mediator.Send(command);
                return CreatedAtAction(nameof(GetPlanTaxConfiguration), new { planId, id = configurationId }, configurationId);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get plan tax configurations
        /// </summary>
        [HttpGet("plans/{planId}/configurations")]
        [ProducesResponseType(typeof(List<PlanTaxConfigurationDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetPlanTaxConfigurations(Guid planId)
        {
            try
            {
                // This would need a specific query implementation
                return Ok(new List<PlanTaxConfigurationDto>());
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get specific plan tax configuration
        /// </summary>
        [HttpGet("plans/{planId}/configurations/{id}")]
        [ProducesResponseType(typeof(PlanTaxConfigurationDto), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetPlanTaxConfiguration(Guid planId, Guid id)
        {
            try
            {
                // This would need a specific query implementation
                return NotFound("Plan tax configuration not found");
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Create tax category
        /// </summary>
        [HttpPost("categories")]
        [ProducesResponseType(typeof(Guid), StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> CreateTaxCategory([FromBody] CreateTaxCategoryDto request)
        {
            try
            {
                var command = new CreateTaxCategoryCommand
                {
                    Name = request.Name,
                    Description = request.Description,
                    Code = request.Code,
                    CreatedByUserId = GetCurrentUserId()
                };

                var categoryId = await Mediator.Send(command);
                return CreatedAtAction(nameof(GetTaxCategory), new { id = categoryId }, categoryId);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get tax categories
        /// </summary>
        [HttpGet("categories")]
        [ProducesResponseType(typeof(List<TaxCategoryDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetTaxCategories(
            [FromQuery] bool activeOnly = true,
            [FromQuery] string? region = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10,
            [FromQuery] string? searchTerm = null)
        {
            try
            {
                var query = new GetTaxCategoriesQuery
                {
                    ActiveOnly = activeOnly,
                    Region = region,
                    Page = page,
                    PageSize = pageSize,
                    SearchTerm = searchTerm
                };

                var categories = await Mediator.Send(query);
                return Ok(categories);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get specific tax category
        /// </summary>
        [HttpGet("categories/{id}")]
        [ProducesResponseType(typeof(TaxCategoryDto), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetTaxCategory(Guid id)
        {
            try
            {
                // This would need a specific query implementation
                return NotFound("Tax category not found");
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Create tax exemption for a user
        /// </summary>
        [HttpPost("exemptions")]
        [ProducesResponseType(typeof(Guid), StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> CreateTaxExemption([FromBody] CreateTaxExemptionDto request)
        {
            try
            {
                var command = new CreateTaxExemptionCommand
                {
                    UserId = request.UserId,
                    ExemptionType = request.ExemptionType,
                    ExemptionNumber = request.ExemptionNumber,
                    IssuingAuthority = request.IssuingAuthority,
                    ValidFrom = request.ValidFrom,
                    ValidTo = request.ValidTo,
                    ExemptTaxTypes = request.ExemptTaxTypes,
                    ApplicableRegions = request.ApplicableRegions,
                    DocumentPath = request.DocumentPath,
                    CreatedByUserId = GetCurrentUserId()
                };

                var exemptionId = await Mediator.Send(command);
                return CreatedAtAction(nameof(GetTaxExemption), new { id = exemptionId }, exemptionId);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get tax exemptions
        /// </summary>
        [HttpGet("exemptions")]
        [ProducesResponseType(typeof(List<TaxExemptionDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetTaxExemptions(
            [FromQuery] Guid? userId = null,
            [FromQuery] bool activeOnly = true,
            [FromQuery] bool unverifiedOnly = false,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            try
            {
                // This would need a specific query implementation
                return Ok(new List<TaxExemptionDto>());
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get specific tax exemption
        /// </summary>
        [HttpGet("exemptions/{id}")]
        [ProducesResponseType(typeof(TaxExemptionDto), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetTaxExemption(Guid id)
        {
            try
            {
                // This would need a specific query implementation
                return NotFound("Tax exemption not found");
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Verify tax exemption
        /// </summary>
        [HttpPost("exemptions/{id}/verify")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> VerifyTaxExemption(Guid id, [FromBody] VerifyTaxExemptionDto request)
        {
            try
            {
                // This would need a specific command implementation
                return Ok();
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        private new IActionResult HandleException(Exception ex)
        {
            // Use base class exception handling
            return base.HandleException(ex);
        }
    }

    public class VerifyTaxExemptionDto
    {
        public string? VerificationNotes { get; set; }
    }
}
