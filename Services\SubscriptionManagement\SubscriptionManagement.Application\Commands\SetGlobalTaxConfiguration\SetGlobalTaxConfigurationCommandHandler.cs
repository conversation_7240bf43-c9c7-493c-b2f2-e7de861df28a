using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Interfaces;
using SubscriptionManagement.Domain.Services;
using SubscriptionManagement.Domain.ValueObjects;
using Shared.Messaging;

namespace SubscriptionManagement.Application.Commands.SetGlobalTaxConfiguration
{
    public class SetGlobalTaxConfigurationCommandHandler : IRequestHandler<SetGlobalTaxConfigurationCommand, Guid>
    {
        private readonly IGlobalTaxConfigurationRepository _globalTaxConfigurationRepository;
        private readonly IMessageBroker _messageBroker;
        private readonly ILogger<SetGlobalTaxConfigurationCommandHandler> _logger;

        public SetGlobalTaxConfigurationCommandHandler(
            IGlobalTaxConfigurationRepository globalTaxConfigurationRepository,
            IMessageBroker messageBroker,
            ILogger<SetGlobalTaxConfigurationCommandHandler> logger)
        {
            _globalTaxConfigurationRepository = globalTaxConfigurationRepository;
            _messageBroker = messageBroker;
            _logger = logger;
        }

        public async Task<Guid> Handle(SetGlobalTaxConfigurationCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Setting global tax configuration for {TaxType} with rate {Rate}% for regions {Regions}",
                request.TaxType, request.Rate, string.Join(", ", request.ApplicableRegions));

            try
            {
                _logger.LogDebug("Creating tax configuration value object");

                // Create tax configuration value object
                var taxConfiguration = TaxConfiguration.Create(
                    request.TaxType,
                    request.Rate,
                    request.IsIncluded,
                    request.ApplicableRegions,
                    request.EffectiveDate,
                    request.ExpirationDate);

                _logger.LogDebug("Tax configuration created successfully. Validating business rules...");

                // Validate tax configuration
                foreach (var region in request.ApplicableRegions)
                {
                    _logger.LogDebug("Validating tax configuration for region: {Region}", region);
                    TaxDomainService.ValidateTaxConfiguration(taxConfiguration, region);
                }

                _logger.LogDebug("Tax configuration validation completed successfully");

                // Check if configuration already exists for this tax type and regions
                _logger.LogDebug("Checking for existing configuration for tax type {TaxType} and region {Region}",
                    request.TaxType, request.ApplicableRegions.First());

                var existingConfig = await _globalTaxConfigurationRepository
                    .GetByTaxTypeAndRegionAsync(request.TaxType, request.ApplicableRegions.First());

                GlobalTaxConfiguration globalTaxConfig;

                if (existingConfig != null)
                {
                    _logger.LogDebug("Found existing configuration {Id}. Updating...", existingConfig.Id);

                    // Update existing configuration
                    existingConfig.UpdateTaxConfiguration(taxConfiguration, request.CreatedByUserId, request.Description);
                    existingConfig.UpdatePriority(request.Priority, request.CreatedByUserId);
                    globalTaxConfig = await _globalTaxConfigurationRepository.UpdateAsync(existingConfig);

                    _logger.LogInformation("Updated existing global tax configuration {Id}", existingConfig.Id);
                }
                else
                {
                    _logger.LogDebug("No existing configuration found. Creating new one...");

                    // Create new configuration
                    globalTaxConfig = new GlobalTaxConfiguration(
                        taxConfiguration,
                        request.Priority,
                        request.CreatedByUserId,
                        request.Description);

                    _logger.LogDebug("Saving new global tax configuration to database...");
                    globalTaxConfig = await _globalTaxConfigurationRepository.AddAsync(globalTaxConfig);

                    _logger.LogInformation("Created new global tax configuration {Id}", globalTaxConfig.Id);
                }

                // Publish integration event
                await _messageBroker.PublishAsync("tax.global_configuration_changed", new
                {
                    ConfigurationId = globalTaxConfig.Id,
                    TaxType = request.TaxType.ToString(),
                    Rate = request.Rate,
                    IsIncluded = request.IsIncluded,
                    ApplicableRegions = request.ApplicableRegions,
                    EffectiveDate = request.EffectiveDate,
                    ExpirationDate = request.ExpirationDate,
                    Priority = request.Priority,
                    CreatedByUserId = request.CreatedByUserId,
                    CreatedAt = DateTime.UtcNow
                });

                _logger.LogInformation("Successfully set global tax configuration {Id} for {TaxType}",
                    globalTaxConfig.Id, request.TaxType);

                return globalTaxConfig.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting global tax configuration for {TaxType}", request.TaxType);
                throw;
            }
        }
    }
}
