using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Exceptions;
using SubscriptionManagement.Domain.Interfaces;
using SubscriptionManagement.Domain.ValueObjects;

namespace SubscriptionManagement.Domain.Services
{
    public class TaxDomainService
    {
        /// <summary>
        /// Validate tax configuration business rules
        /// </summary>
        public static void ValidateTaxConfiguration(TaxConfiguration taxConfiguration, string region)
        {
            if (taxConfiguration == null)
                throw new SubscriptionDomainException("Tax configuration cannot be null");

            if (string.IsNullOrWhiteSpace(region))
                throw new SubscriptionDomainException("Region cannot be empty");

            if (!taxConfiguration.IsApplicableToRegion(region))
                throw new SubscriptionDomainException($"Tax configuration is not applicable to region {region}");

            // For new configurations, we don't need to check if they're currently active
            // We only validate that the effective date is not too far in the past
            var maxPastDays = 30; // Allow configurations up to 30 days in the past
            if (taxConfiguration.EffectiveDate < DateTime.UtcNow.AddDays(-maxPastDays))
                throw new SubscriptionDomainException($"Tax configuration effective date cannot be more than {maxPastDays} days in the past");

            // Business rule: GST rates in India should not exceed 28%
            if (taxConfiguration.TaxType == TaxType.GST &&
                taxConfiguration.ApplicableRegions.Contains("IN") &&
                taxConfiguration.Rate > 28)
            {
                throw new SubscriptionDomainException("GST rate in India cannot exceed 28%");
            }

            // Business rule: TDS rates should not exceed 30%
            if (taxConfiguration.TaxType == TaxType.TDS && taxConfiguration.Rate > 30)
            {
                throw new SubscriptionDomainException("TDS rate cannot exceed 30%");
            }
        }

        /// <summary>
        /// Calculate compound tax following Indian tax rules
        /// </summary>
        public static Money CalculateCompoundTax(Money baseAmount, List<TaxConfiguration> taxConfigurations, string region)
        {
            if (baseAmount == null)
                throw new SubscriptionDomainException("Base amount cannot be null");

            if (taxConfigurations == null || !taxConfigurations.Any())
                return Money.Zero(baseAmount.Currency);

            var totalTax = Money.Zero(baseAmount.Currency);
            var currentTaxableAmount = baseAmount;

            // Sort tax configurations by priority (GST components first, then other taxes)
            var sortedConfigs = taxConfigurations
                .Where(tc => tc.IsApplicableToRegion(region) && tc.IsActiveOn(DateTime.UtcNow))
                .OrderBy(tc => GetTaxPriority(tc.TaxType))
                .ToList();

            foreach (var config in sortedConfigs)
            {
                var taxAmount = config.CalculateTax(currentTaxableAmount, region);
                totalTax = totalTax.Add(taxAmount);

                // For compound taxation, add tax to base for next calculation
                // This is typically done for cascading taxes
                if (ShouldCompoundTax(config.TaxType))
                {
                    currentTaxableAmount = currentTaxableAmount.Add(taxAmount);
                }
            }

            return totalTax;
        }

        /// <summary>
        /// Determine tax calculation priority
        /// </summary>
        public static int GetTaxPriority(TaxType taxType)
        {
            return taxType switch
            {
                TaxType.CGST => 1,
                TaxType.SGST => 2,
                TaxType.IGST => 3,
                TaxType.UTGST => 4,
                TaxType.GST => 5,
                TaxType.Cess => 6,
                TaxType.VAT => 7,
                TaxType.ServiceTax => 8,
                TaxType.SalesTax => 9,
                TaxType.TDS => 10,
                TaxType.TCS => 11,
                _ => 99
            };
        }

        /// <summary>
        /// Determine if tax should be compounded
        /// </summary>
        public static bool ShouldCompoundTax(TaxType taxType)
        {
            return taxType switch
            {
                TaxType.Cess => true, // Cess is calculated on GST + base amount
                _ => false
            };
        }

        /// <summary>
        /// Validate tax exemption business rules
        /// </summary>
        public static void ValidateTaxExemption(TaxExemption exemption, TaxType taxType, string region)
        {
            if (exemption == null)
                throw new SubscriptionDomainException("Tax exemption cannot be null");

            if (!exemption.IsValidOn(DateTime.UtcNow))
                throw new SubscriptionDomainException("Tax exemption is not currently valid");

            if (!exemption.IsExemptFromTax(taxType, region))
                throw new SubscriptionDomainException($"Tax exemption does not apply to {taxType} in {region}");

            if (exemption.RequiresVerification())
                throw new SubscriptionDomainException("Tax exemption requires verification before use");
        }

        /// <summary>
        /// Calculate tax-inclusive to tax-exclusive conversion
        /// </summary>
        public static Money CalculateBaseAmountFromTaxInclusive(Money taxInclusiveAmount, decimal totalTaxRate)
        {
            if (taxInclusiveAmount == null)
                throw new SubscriptionDomainException("Tax inclusive amount cannot be null");

            if (totalTaxRate < 0)
                throw new SubscriptionDomainException("Tax rate cannot be negative");

            if (totalTaxRate == 0)
                return taxInclusiveAmount;

            var baseAmount = taxInclusiveAmount.Amount / (1 + (totalTaxRate / 100));
            return Money.Create(baseAmount, taxInclusiveAmount.Currency);
        }

        /// <summary>
        /// Validate region-specific tax rules
        /// </summary>
        public static void ValidateRegionTaxRules(string region, List<TaxConfiguration> taxConfigurations)
        {
            if (string.IsNullOrWhiteSpace(region))
                throw new SubscriptionDomainException("Region cannot be empty");

            var applicableConfigs = taxConfigurations
                .Where(tc => tc.IsApplicableToRegion(region) && tc.IsActiveOn(DateTime.UtcNow))
                .ToList();

            // India-specific validation
            if (region.Equals("IN", StringComparison.OrdinalIgnoreCase))
            {
                ValidateIndianTaxRules(applicableConfigs);
            }

            // Check for conflicting tax configurations
            var duplicateTypes = applicableConfigs
                .GroupBy(tc => tc.TaxType)
                .Where(g => g.Count() > 1)
                .Select(g => g.Key)
                .ToList();

            if (duplicateTypes.Any())
            {
                throw new SubscriptionDomainException($"Multiple configurations found for tax types: {string.Join(", ", duplicateTypes)}");
            }
        }

        /// <summary>
        /// Validate India-specific tax rules
        /// </summary>
        private static void ValidateIndianTaxRules(List<TaxConfiguration> taxConfigurations)
        {
            var gstTypes = new[] { TaxType.GST, TaxType.CGST, TaxType.SGST, TaxType.IGST, TaxType.UTGST };
            var gstConfigs = taxConfigurations.Where(tc => gstTypes.Contains(tc.TaxType)).ToList();

            // Rule: Cannot have both IGST and CGST+SGST for the same transaction
            var hasIGST = gstConfigs.Any(tc => tc.TaxType == TaxType.IGST);
            var hasCGSTSGST = gstConfigs.Any(tc => tc.TaxType == TaxType.CGST) &&
                              gstConfigs.Any(tc => tc.TaxType == TaxType.SGST);

            if (hasIGST && hasCGSTSGST)
            {
                throw new SubscriptionDomainException("Cannot have both IGST and CGST+SGST configurations for the same region");
            }

            // Rule: CGST and SGST rates should be equal
            var cgstConfig = gstConfigs.FirstOrDefault(tc => tc.TaxType == TaxType.CGST);
            var sgstConfig = gstConfigs.FirstOrDefault(tc => tc.TaxType == TaxType.SGST);

            if (cgstConfig != null && sgstConfig != null && cgstConfig.Rate != sgstConfig.Rate)
            {
                throw new SubscriptionDomainException("CGST and SGST rates must be equal");
            }

            // Rule: Total GST rate validation
            var totalGSTRate = gstConfigs.Sum(tc => tc.Rate);
            if (totalGSTRate > 28)
            {
                throw new SubscriptionDomainException("Total GST rate cannot exceed 28% in India");
            }
        }

        /// <summary>
        /// Determine if tax should be displayed separately
        /// </summary>
        public static bool ShouldDisplayTaxSeparately(string region, List<TaxConfiguration> taxConfigurations)
        {
            // In India, taxes are typically displayed separately for B2B transactions
            if (region.Equals("IN", StringComparison.OrdinalIgnoreCase))
            {
                return true;
            }

            // Check if any configuration specifies tax-exclusive pricing
            return taxConfigurations.Any(tc => !tc.IsIncluded);
        }

        /// <summary>
        /// Calculate effective tax rate for a region
        /// </summary>
        public static decimal CalculateEffectiveTaxRate(List<TaxConfiguration> taxConfigurations, string region)
        {
            var applicableConfigs = taxConfigurations
                .Where(tc => tc.IsApplicableToRegion(region) && tc.IsActiveOn(DateTime.UtcNow))
                .ToList();

            if (!applicableConfigs.Any())
                return 0;

            // For simple addition (most common case)
            return applicableConfigs.Sum(tc => tc.Rate);
        }
    }
}
