using SubscriptionManagement.Application;
using SubscriptionManagement.Infrastructure;
using SubscriptionManagement.Infrastructure.Persistence;
using SubscriptionManagement.API.Middleware;
using Shared.Messaging;
using Shared.Infrastructure.Caching;
using Shared.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using Microsoft.OpenApi.Models;

var builder = WebApplication.CreateBuilder(args);
var configuration = builder.Configuration;

// Add services to the container
builder.Services.AddControllers();

// Add database
var connectionString = configuration.GetConnectionString("DefaultConnection");
if (connectionString == "InMemory")
{
    builder.Services.AddDbContext<SubscriptionDbContext>(options =>
        options.UseInMemoryDatabase("SubscriptionManagement"));
}
else
{
    builder.Services.AddDbContext<SubscriptionDbContext>(options =>
        options.UseNpgsql(connectionString,
            b =>
            {
                b.MigrationsAssembly(typeof(SubscriptionDbContext).Assembly.FullName);
                b.MigrationsHistoryTable("__EFMigrationsHistory", "subscription");
            }));
}

// Add layers
builder.Services.AddApplication();
builder.Services.AddInfrastructure(configuration);

// Add caching
builder.Services.AddMemoryCache();

// Add messaging
try
{
    builder.Services.AddMessaging(configuration.GetValue<string>("RabbitMQ:Host", "localhost"));
}
catch
{
    // RabbitMQ is optional for development
}

// Add JWT Authentication
var jwtSettings = configuration.GetSection("JwtSettings");
var key = Encoding.ASCII.GetBytes(jwtSettings["Secret"] ?? "your-super-secret-key-that-is-at-least-32-characters-long");

builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.RequireHttpsMetadata = false;
    options.SaveToken = true;
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(key),
        ValidateIssuer = true,
        ValidIssuer = jwtSettings["Issuer"],
        ValidateAudience = true,
        ValidAudience = jwtSettings["Audience"],
        ValidateLifetime = true,
        ClockSkew = TimeSpan.Zero
    };
});

// Add Swagger
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "Subscription Management API",
        Version = "v1",
        Description = "API for managing subscriptions, plans, and payments"
    });

    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            new string[] {}
        }
    });
});

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// Add health checks
var healthChecks = builder.Services.AddHealthChecks();
// Temporarily disable AddNpgSql to debug connection string issue
// if (connectionString != "InMemory" && !string.IsNullOrEmpty(connectionString))
// {
//     healthChecks.AddNpgSql(connectionString);
// }
var redisConnection = builder.Configuration.GetConnectionString("Redis");
if (!string.IsNullOrEmpty(redisConnection))
{
    try
    {
        healthChecks.AddRedis(redisConnection);
    }
    catch
    {
        // Redis is optional for development
    }
}

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Subscription Management API V1");
        c.RoutePrefix = string.Empty; // Set Swagger UI at the app's root
    });
}

app.UseHttpsRedirection();
app.UseCors("AllowAll");

// Add exception handling middleware
app.UseExceptionHandling();

// Add metrics middleware
// Temporarily commented out due to Infrastructure layer issues
// app.UseMetrics();

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

// Enhanced health checks
app.MapHealthChecks("/health");
app.MapHealthChecks("/health/detailed", new Microsoft.AspNetCore.Diagnostics.HealthChecks.HealthCheckOptions
{
    ResponseWriter = async (context, report) =>
    {
        context.Response.ContentType = "application/json";
        var response = new
        {
            status = report.Status.ToString(),
            checks = report.Entries.Select(x => new
            {
                name = x.Key,
                status = x.Value.Status.ToString(),
                exception = x.Value.Exception?.Message,
                duration = x.Value.Duration.ToString(),
                data = x.Value.Data
            }),
            totalDuration = report.TotalDuration.ToString()
        };
        await context.Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(response));
    }
});

// Auto-migrate database in development
// Temporarily commented out due to Infrastructure layer issues
// if (app.Environment.IsDevelopment())
// {
//     using var scope = app.Services.CreateScope();
//     var context = scope.ServiceProvider.GetRequiredService<SubscriptionDbContext>();
//     await context.Database.MigrateAsync();
// }

app.Run();
