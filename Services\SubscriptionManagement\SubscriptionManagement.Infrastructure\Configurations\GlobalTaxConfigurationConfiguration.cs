using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SubscriptionManagement.Domain.Entities;

namespace SubscriptionManagement.Infrastructure.Configurations
{
    public class GlobalTaxConfigurationConfiguration : IEntityTypeConfiguration<GlobalTaxConfiguration>
    {
        public void Configure(EntityTypeBuilder<GlobalTaxConfiguration> builder)
        {
            builder.ToTable("GlobalTaxConfigurations");

            builder.<PERSON>Key(gtc => gtc.Id);

            builder.Property(gtc => gtc.IsActive)
                .IsRequired();

            builder.Property(gtc => gtc.Priority)
                .IsRequired();

            builder.Property(gtc => gtc.Description)
                .HasMaxLength(500);

            builder.Property(gtc => gtc.CreatedByUserId)
                .IsRequired();

            builder.Property(gtc => gtc.ModifiedByUserId);

            builder.Property(gtc => gtc.CreatedAt)
                .IsRequired();

            builder.Property(gtc => gtc.UpdatedAt);

            // Configure TaxConfiguration as owned entity
            builder.OwnsOne(gtc => gtc.TaxConfiguration, taxConfig =>
            {
                taxConfig.Property(c => c.TaxType)
                    .HasColumnName("TaxType")
                    .HasConversion<string>()
                    .IsRequired();

                taxConfig.Property(c => c.Rate)
                    .HasColumnName("Rate")
                    .HasColumnType("decimal(5,4)")
                    .IsRequired();

                taxConfig.Property(c => c.IsIncluded)
                    .HasColumnName("IsIncluded")
                    .IsRequired();

                taxConfig.Property(c => c.EffectiveDate)
                    .HasColumnName("EffectiveDate")
                    .IsRequired();

                taxConfig.Property(c => c.ExpirationDate)
                    .HasColumnName("ExpirationDate");

                // Store ApplicableRegions as comma-separated string
                // Note: This approach requires in-memory filtering for Contains operations
                // Consider using a separate table for regions in the future for better query performance
                taxConfig.Property(c => c.ApplicableRegions)
                    .HasConversion(
                        v => string.Join(',', v),
                        v => v.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList())
                    .HasColumnName("ApplicableRegions")
                    .HasMaxLength(1000);
            });

            // Indexes
            builder.HasIndex(gtc => gtc.IsActive);

            builder.HasIndex(gtc => gtc.Priority);

            builder.HasIndex(gtc => gtc.CreatedByUserId);

            builder.HasIndex(gtc => gtc.CreatedAt);

            // Indexes for owned entity properties need to be created differently
            // These will be created on the actual column names in the database

            // Composite indexes for common queries
            builder.HasIndex(gtc => new { gtc.IsActive, gtc.Priority });
        }
    }
}
